"use client";

import {
  extractAsinFromUrl,
  parseAmazonProductPage,
} from "@/lib/api/productApi";
import { ProductData } from "@/lib/types/home";
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useReducer,
  useState,
} from "react";
import { useGetKeepaData } from "./useGetKeepaData";
import { getCurrentBuyBoxPrice } from "../lib/utils/index";


interface DataContextType {
  productData: ProductData | null;
  setProductData: React.Dispatch<React.SetStateAction<ProductData | null>>;
  selectedCountry: "GB" | "US";
  setSelectedCountry: React.Dispatch<React.SetStateAction<"GB" | "US">>;
  isLoading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  dataLoadingProgress: number;
  setDataLoadingProgress: React.Dispatch<React.SetStateAction<number>>;
  pageUrl: string | null;
  setPageUrl: React.Dispatch<React.SetStateAction<string | null>>;
  isVatRegistered: boolean;
  setIsVatRegistered: React.Dispatch<React.SetStateAction<boolean>>;
  isGated: boolean | undefined;
  setIsGated: React.Dispatch<React.SetStateAction<boolean | undefined>>;
  vat: number;
  setVat: React.Dispatch<React.SetStateAction<number>>;
}

const ProductContext = createContext<DataContextType | undefined>(undefined);

export const ProductProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [productData, setProductData] = useState<ProductData | null>(null);
  const [selectedCountry, setSelectedCountry] = useState<"GB" | "US">("GB");
  const [isLoading, setIsLoading] = useState(true);
  const [dataLoadingProgress, setDataLoadingProgress] = useState(0);
  const [pageUrl, setPageUrl] = useState<string | null>(null);
  const [isVatRegistered, setIsVatRegistered] = useState(false);
  const [isGated, setIsGated] = useState<boolean | undefined>(undefined);
  const [vat, setVat] = useState<number>(20)

  const { data } = useGetKeepaData({
    KeepaRequest: {
      asin: productData?.asin || "",
      country: selectedCountry || "GB",
      vat: vat,
      sellerPrice: productData && getCurrentBuyBoxPrice({
        isVatRegistered: isVatRegistered,
        productData: productData,
      }) || 0,
    },
  });

  console.log('data', data);

  const handleMessage = useCallback((event: MessageEvent) => {
    if (event.data.url) {
      if (event.data.url.includes(".com")) {
        setSelectedCountry("US");
      } else if (event.data.url.includes(".co.uk")) {
        setSelectedCountry("GB");
      }
    }

    console.log("Received message from extension:", event.data);
    if (event.data && event.data.type === "productData") {
      try {
        const data = event.data.data;
        const stage = event.data.stage || "unknown";

        if (isLoading) {
          setIsLoading(false);
        }

        // Update loading progress based on stage
        if (dataLoadingProgress < 100) {  
          if (stage === "basic") {
            setDataLoadingProgress(25);
          } else if (stage === "medium") {
            setDataLoadingProgress(50);
          } else if (stage === "complete") {
            // Add a slight delay before reaching 100%
            setDataLoadingProgress(75);
            setTimeout(() => {
              setDataLoadingProgress(100);
            }, 1000);
          }
        }

        setProductData((prevState) => {
          if (!prevState) {
            return data;
          }

          return {
            ...prevState,
            ...data,
            estimated_sales:
              data.estimated_sales !== undefined
                ? data.estimated_sales
                : prevState.estimated_sales,
            mainImage: prevState.mainImage || data.mainImage,
            offers: data.offers || prevState.offers,
            features: data.features || prevState.features,
            pricing: {
              ...prevState.pricing,
              ...(data.pricing || {}),
            },
            ...(prevState.graph_data
              ? { graph_data: prevState.graph_data }
              : {}),
          };
        });

        // fetchEnhancedProductData(data.asin);
      } catch (err) {
        console.error("Error processing product data message:", err);
        // updateSectionError(
        //   "basic",
        //   "Error processing product data. Please refresh.",
        // );
      }
    }
  }, []);

  // Effect for setting up message listeners
  useEffect(() => {
    window.addEventListener("message", handleMessage);

    // Signal to the parent that we're ready to receive data - BUT ONLY ONCE
    if (window.parent) {
      try {
        window.parent.postMessage({ type: "ready", debug: Date.now() }, "*");
        // setHasSignaledReady(true);
      } catch (err) {
        console.error("Error sending ready message:", err);
        // setError("Failed to communicate with extension. Please refresh.");
        setIsLoading(false);
      }
    }

    // Cleanup listener on unmount
    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, [handleMessage]);

  return (
    <ProductContext.Provider
      value={{
        productData,
        setProductData,
        selectedCountry,
        setSelectedCountry,
        isLoading,
        setIsLoading,
        dataLoadingProgress,
        setDataLoadingProgress,
        pageUrl,
        setPageUrl,
        isVatRegistered,
        setIsVatRegistered,
        isGated,
        setIsGated,
        vat,
        setVat,
      }}
    >
      {children}
    </ProductContext.Provider>
  );
};

export const useProductContext = () => {
  const context = useContext(ProductContext);
  if (!context) {
    throw new Error("useProductContext must be used within a ProductProvider");
  }
  return context;
};
