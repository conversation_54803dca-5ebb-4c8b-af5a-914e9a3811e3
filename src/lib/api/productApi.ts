// productApi.ts
import { tokenErrorToast } from "@/components/toast";
import { ProductData } from "../types/home";
import { AUTH_COOKIES } from "../utils/cookie";
import { storageService } from "./storage";
import React from "react";
import { createRoot } from "react-dom/client";

const API_BASE_URL = "https://app.clickbuy.ai/extension-backend/api/v1";

// Mock data for unauthenticated users (keeping existing mock data)
const mockData = {
  summary: {
    asin: "",
    title: "",
    brand: "",
    category: "",
    rating: 0,
    reviews: 0,
    ean: [""],
    features: [],
    description: "",
  },
  pricing: {
    new: { price: 0, domain: "UK" },
    fba_fees: { price: 0, domain: "UK" },
    buy_box_price: { price: 0, domain: "UK" },
    sales_rank: 0,
    referral_fee: {
      referral_fee_percent: 0,
      referral_fee_percentage: 0,
      domain: "UK",
    },
  },
  offers: {
    offers: 0,
    fbm: 0,
    fba: 0,
    sellers_offers: [],
  },
  warnings: {
    variations: {
      variations_value: [],
      is_variations: false,
    },
    adult_product: false,
    meltable: false,
    amz_in_buy_box: false,
  },
  dimensions: {
    height: { value: 0, unit: "cm" },
    length: { value: 0, unit: "cm" },
    width: { value: 0, unit: "cm" },
    weight: { value: 0, unit: "kg" },
  },
};

// Keep existing interfaces
export interface ProductApiResponse {
  summary: {
    asin: string;
    title: string;
    brand: string;
    category: string;
    rating: number;
    reviews: number;
    ean: string[];
    features: string[];
    description: string;
    size?: string;
    monthly_sold?: number;
    image_url?: string; // Add image_url property
  };
  pricing: {
    new: {
      price: number;
      domain: string;
    };
    fba_fees: {
      price: number;
      domain: string;
    };
    buy_box_price: {
      price: number;
      domain: string;
    };
    sales_rank: number;
    referral_fee: {
      referral_fee_percent: number;
      referral_fee_percentage: number;
      domain: string;
    };
  };
  offers: {
    offers: number;
    fbm: number;
    fba: number;
    sellers_offers: Array<{
      seller_id: string;
      price: number;
      stock: number;
      is_fba: boolean;
      is_amazon: boolean;
    }>;
  };
  warnings: {
    variations: {
      variations_value: Array<{
        asin: string;
        attributes: Array<{
          dimension: string;
          value: string;
        }>;
      }>;
      is_variations: boolean;
    };
    adult_product: boolean;
    meltable: boolean;
    amz_in_buy_box: boolean;
  };
  rank_and_price_history?: {
    current: {
      new: { price: number; domain: string };
      sales_rank: number;
      rating: number;
      reviews: number;
      offers: number;
      buy_box_price: { price: number; domain: string };
      fbm: number;
      fba: number;
    };
    avg: {
      new: { price: number; domain: string };
      sales_rank: number;
      rating: number;
      reviews: number;
      offers: number;
      buy_box_price: { price: number; domain: string };
      fbm: number;
      fba: number;
    };
    avg30: {
      new: { price: number; domain: string };
      sales_rank: number;
      rating: number;
      reviews: number;
      offers: number;
      buy_box_price: { price: number; domain: string };
      fbm: number;
      fba: number;
    };
    avg90: {
      new: { price: number; domain: string };
      sales_rank: number;
      rating: number;
      reviews: number;
      offers: number;
      buy_box_price: { price: number; domain: string };
      fbm: number;
      fba: number;
    };
    avg180: {
      new: { price: number; domain: string };
      sales_rank: number;
      rating: number;
      reviews: number;
      offers: number;
      buy_box_price: { price: number; domain: string };
      fbm: number;
      fba: number;
    };
    avg365: {
      new: { price: number; domain: string };
      sales_rank: number;
      rating: number;
      reviews: number;
      offers: number;
      buy_box_price: { price: number; domain: string };
      fbm: number;
      fba: number;
    };
  };
  monthly_sold?: number;
  graph_data?: {
    amazon: { time: string[]; data: (number | null)[] };
    new: { time: string[]; data: (number | null)[] };
    sales_rank: { time: string[]; data: (number | null)[] };
    fbm: { time: string[]; data: (number | null)[] };
    fba: { time: string[]; data: (number | null)[] };
    buy_box: { time: string[]; data: (number | null)[] };
    count_new: { time: string[]; data: (number | null)[] };
    count_reviews: { time: string[]; data: (number | null)[] };
    rating: { time: string[]; data: (number | null)[] };
  };
  dimensions: {
    height: { value: number; unit: string };
    length: { value: number; unit: string };
    width: { value: number; unit: string };
    weight: { value: number; unit: string };
  };
  buy_box_history?: {
    "30_days": Record<string, number>;
    "90_days": Record<string, number>;
    "180_days": Record<string, number>;
    "365_days": Record<string, number>;
  };
}

// New interface for profit calculator response
export interface ProfitCalculatorResponse {
  buy_box_price: number;
  fba_fee: number;
  profit: number;
  referral_fee: number;
  roi: number;
  total_fee: number;
  variable_closing_fee: number;
}

// Existing mapping function
export function mapApiResponseToProductData(
  apiData: ProductApiResponse,
): ProductData {
  // Add some debugging to verify data structure

  // First, handle the warnings object to preserve variations data
  const warningsObject = {
    adult_product: apiData.warnings.adult_product,
    amz_in_buy_box: apiData.warnings.amz_in_buy_box,
    meltable: apiData.warnings.meltable,
    // Add variations if it exists in the API response
    ...(apiData.warnings.variations && {
      variations: apiData.warnings.variations,
    }),
  };

  return {
    title: apiData.summary.title,
    asin: apiData.summary.asin,
    price: apiData.pricing.new.price
      ? `£${apiData.pricing.new.price.toFixed(2)}`
      : "£0.00",
    rating: apiData.summary.rating?.toString() || "N/A",
    reviewCount: apiData.summary.reviews?.toString() || "N/A",
    brand: apiData.summary.brand,
    category: apiData.summary.category,
    features: apiData.summary.features || [],
    bsr: apiData.pricing?.sales_rank?.toString() || "N/A",
    mainImage: apiData.summary.image_url,
    url: `https://www.amazon.co.uk/dp/${apiData.summary.asin}`,
    summary: {
      title: apiData.summary.title,
      brand: apiData.summary.brand,
      category: apiData.summary.category,
      rating: apiData.summary.rating?.toString() || "N/A",
      reviews: apiData.summary.reviews?.toString() || "N/A",
      features: apiData.summary.features || [],
      ean: Array.isArray(apiData.summary.ean)
        ? apiData.summary.ean[0]
        : apiData.summary.ean,
    },
    pricing: {
      non_vat_pricing: {
        buy_box_price: apiData.pricing.buy_box_price.price,
        fba_fee: apiData.pricing.fba_fees.price,
        profit: calculateProfit(apiData),
        referral_fee: calculateReferralFee(apiData),
        roi: calculateROI(apiData),
        sales_rank: apiData.pricing.sales_rank,
        seller_price: apiData.pricing.new.price,
        total_fee:
          apiData.pricing.fba_fees.price + calculateReferralFee(apiData),
        variable_closing_fee: 0,
      },
    },
    offers: {
      total: apiData.offers.offers,
      fba: apiData.offers.fba,
      fbm: apiData.offers.fbm,
      sellers_offers: apiData.offers.sellers_offers,
    },
    // Use our updated warnings object that includes variations
    warnings: warningsObject,
    dimensions: apiData.dimensions,
    graph_data: apiData.graph_data,
    rank_and_price_history: apiData.rank_and_price_history,
    // Include estimated sales if available
    ...(typeof apiData.summary.monthly_sold === "number" && {
      estimated_sales: apiData.summary.monthly_sold,
    }),
  };
}

// Helper functions for calculations
function calculateProfit(apiData: ProductApiResponse): number {
  // Simple profit calculation: seller price - fees
  const sellerPrice = apiData.pricing.new.price;
  const fbaFee = apiData.pricing.fba_fees.price;
  const referralFee =
    sellerPrice * (apiData.pricing.referral_fee.referral_fee_percentage / 100);
  return sellerPrice - fbaFee - referralFee;
}

function calculateReferralFee(apiData: ProductApiResponse): number {
  // Calculate referral fee based on percentage
  return (
    apiData.pricing.new.price *
    (apiData.pricing.referral_fee.referral_fee_percentage / 100)
  );
}

function calculateROI(apiData: ProductApiResponse): number {
  // ROI calculation
  const profit = calculateProfit(apiData);
  const cost = apiData.pricing.new.price;
  return cost > 0 ? (profit / cost) * 100 : 0;
}

/**
 * Get access token from our storage service
 */
function getAccessToken(): string | null {
  const token = storageService.getItem(AUTH_COOKIES.ACCESS_TOKEN);
  return token;
}

/**
 * Fetch product data from the API including Keepa data with two-stage loading
 * Stage 1: Quick data with full_response=false for fast overview display
 * Stage 2: Full data with full_response=true for complete details
 */
export async function fetchProductData(
  asin: string,
  country = "UK",
  vat = 1.2,
  sellerPrice = 0,
): Promise<ProductApiResponse> {
  try {
    const accessToken = getAccessToken();

    if (!accessToken) {
      return { ...mockData, summary: { ...mockData.summary, asin } };
    }

    // Stage 1: Fetch quick data without full response for fast overview
    const quickResponse = await fetch(
      `${API_BASE_URL}/kepa/get-keepa-product/${asin}?country=${country}&vat=${vat}&seller_price=${sellerPrice}&full_response=false`,
      {
        method: "GET",
        headers: {
          accept: "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );

    if (!quickResponse.ok) {
      tokenErrorToast(quickResponse);
    }

    const quickData = await quickResponse.json();

    // Immediately start Stage 2: Full data fetch in background
    const fullDataPromise = fetch(
      `${API_BASE_URL}/kepa/get-keepa-product/${asin}?country=${country}&vat=${vat}&seller_price=${sellerPrice}&full_response=true`,
      {
        method: "GET",
        headers: {
          accept: "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      },
    ).then(async (response) => {
      if (!response.ok) {
        if (response.status === 403) {
          await tokenErrorToast(response);
          return null;
        }

        throw new Error(
          `Stage 2 API fetch failed with status ${response.status}`,
        );
      }
      return response.json();
    });

    // Process quick data
    if (quickData.graph_data) {
      Object.keys(quickData.graph_data).forEach((seriesKey) => {
        const series = quickData.graph_data[seriesKey];
        if (series && series.data) {
          series.data = series.data.map((val: string | undefined) =>
            val === "null" || val === undefined ? null : val,
          );
        }
      });
    }

    // Process and clean quick data
    const processedQuickData = quickData as ProductApiResponse;

    // Mark this as stage 1 data
    (processedQuickData as any)._stage = "quick";

    // Handle Stage 2: Full data processing in background
    fullDataPromise
      .then((fullData) => {
        console.log("Stage 2 data received:", fullData);
        // Process graph data if available
        if (fullData.graph_data) {
          Object.keys(fullData.graph_data).forEach((seriesKey) => {
            const series = fullData.graph_data[seriesKey];
            if (series && series.data) {
              series.data = series.data.map((val: string | undefined) =>
                val === "null" || val === undefined ? null : val,
              );
            }
          });
        }

        // Mark this as stage 2 data
        (fullData as any)._stage = "full";

        // Dispatch event with updated full data for components to consume
        if (typeof window !== "undefined") {
          window.dispatchEvent(
            new CustomEvent("product:full-data-ready", {
              detail: {
                asin,
                data: fullData,
                stage: "full",
              },
            }),
          );
        }
      })
      .catch((error) => {
        console.error(
          `Stage 2 failed for ASIN ${asin}:`,
          error.message || error,
        );

        // Dispatch error event so UI can handle gracefully
        if (typeof window !== "undefined") {
          window.dispatchEvent(
            new CustomEvent("product:full-data-error", {
              detail: {
                asin,
                error: error.message,
                stage: "full",
              },
            }),
          );
        }
      });

    // Return Stage 1 data immediately for fast UI rendering
    return processedQuickData;
  } catch (error) {
    console.error(`Error in two-stage fetch for ASIN ${asin}:`, error);
    return { ...mockData, summary: { ...mockData.summary, asin } };
  }
}

// Add a new function specifically for fetching Keepa data if needed
export async function fetchKeepaData(
  asin: string,
  country = "UK",
): Promise<any> {
  try {
    const accessToken = getAccessToken();

    if (!accessToken) {
      return null;
    }

    const response = await fetch(
      `${API_BASE_URL}/kepa/get-keepa-product/${asin}?country=${country}`,
      {
        method: "GET",
        headers: {
          accept: "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );

    if (!response.ok) {
      if (response.status === 403) {
        await tokenErrorToast(response);
        return null;
      }

      throw new Error(`API returned status ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching Keepa data:", error);
    return null;
  }
}

/**
 * Fetch profit calculator data from the API
 * @param params Profit calculator parameters
 * @returns Profit calculator response with both VAT registered and non-VAT registered data
 */
export async function fetchProfitCalculator(params: {
  buy_box_price: number;
  fba_fees: number;
  referral_percent: number;
  seller_price: number;
  variable_closing_fee?: number;
  sales_rank: number; // Make this required, not optional
  country?: string;
  vat?: number;
  asin_code: string;
}): Promise<any> {
  try {
    const accessToken = getAccessToken();

    if (!accessToken) {
      throw new Error("Authentication required");
    }

    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.append("buy_box_price", params.buy_box_price.toString());
    queryParams.append("fba_fees", params.fba_fees.toString());
    queryParams.append("referral_percent", params.referral_percent.toString());
    queryParams.append("seller_price", params.seller_price.toString());

    // Add optional parameters only if they're provided
    if (params.variable_closing_fee !== undefined) {
      queryParams.append(
        "variable_closing_fee",
        params.variable_closing_fee.toString(),
      );
    }

    if (typeof params.sales_rank === "number" && !isNaN(params.sales_rank)) {
      queryParams.append("sales_rank", params.sales_rank.toString());
    }

    queryParams.append("country", params.country || "UK");

    // Ensure VAT parameter is passed correctly
    // If not provided, default to 20
    const vatValue = params.vat !== undefined ? params.vat : 20;
    queryParams.append("vat", vatValue.toString());

    const apiUrl = `${API_BASE_URL}/kepa/profit-calculator?${queryParams.toString()}`;
    const response = await fetch(apiUrl, {
      method: "GET",
      headers: {
        accept: "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
    });

    if (!response.ok) {
      if (response.status === 403) {
        await tokenErrorToast(response);
        return null;
      }

      const errorText = await response.text();
      console.error("API error response:", errorText);
      throw new Error(`API error: ${response.status}`);
    }

    // Parse the response once
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error in profit calculator API:", error);
    throw error; // Let the component handle the error
  }
}

/**
 * Extract ASIN from an Amazon URL
 * @param url Amazon product URL
 * @returns ASIN string or null if not found
 */
export function extractAsinFromUrl(url: string): string | null {
  // Match ASIN pattern in URL
  const asinPattern = /\/(?:dp|gp\/product|asin)\/([A-Z0-9]{10})/i;
  const match = url.match(asinPattern);

  if (match && match[1]) {
    return match[1];
  }

  return null;
}

/**
 * Parse amazon product data from page HTML
 * @param htmlContent Amazon product page HTML
 * @returns Parsed product data
 */
export function parseAmazonProductPage(htmlContent: string): {
  title?: string;
  brand?: string;
  asin?: string;
  price?: string;
  mainImage?: string;
} {
  const result: {
    title?: string;
    brand?: string;
    asin?: string;
    price?: string;
    mainImage?: string;
  } = {};

  try {
    // Extract title
    const titleMatch = htmlContent.match(
      /<span id="productTitle" class="[^"]*">([^<]+)<\/span>/,
    );
    if (titleMatch && titleMatch[1]) {
      result.title = titleMatch[1].trim();
    }

    // Extract brand
    const brandMatch = htmlContent.match(/id="bylineInfo"[^>]*>([^<]+)<\/a>/);
    if (brandMatch && brandMatch[1]) {
      result.brand = brandMatch[1].replace(/Visit the |Store/, "").trim();
    }

    // Extract ASIN
    const asinMatch = htmlContent.match(/ASIN\s*:\s*([A-Z0-9]{10})/);
    if (asinMatch && asinMatch[1]) {
      result.asin = asinMatch[1];
    }

    // Extract price
    const priceMatch = htmlContent.match(
      /id="priceblock_ourprice"[^>]*>([^<]+)<\/span>|class="a-offscreen">([^<]+)<\/span>/,
    );
    if (priceMatch) {
      result.price = (priceMatch[1] || priceMatch[2]).trim();
    }

    // Extract main image
    const imageMatch = htmlContent.match(/id="landingImage"[^>]*src="([^"]+)"/);
    if (imageMatch && imageMatch[1]) {
      result.mainImage = imageMatch[1];
    }
  } catch (error) {
    console.error("Error parsing Amazon product page:", error);
  }

  return result;
}

/**
 * Fetch gated product status from the API
 * @param asin Amazon product ASIN
 * @param countryCode Country code (default: UK)
 * @returns Gated product information
 */
export async function fetchGatedStatus(
  asin: string,
  countryCode = "UK",
): Promise<any> {
  try {
    const accessToken = getAccessToken();

    if (!accessToken) {
      return {
        gated: false,
        categories: [],
        reason: "Unavailable without authentication",
      };
    }

    const response = await fetch(
      `${API_BASE_URL}/amazon/gated/${asin}?country_code=${countryCode}`,
      {
        method: "GET",
        headers: {
          accept: "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );

    if (!response.ok) {
      // Handle 401 unauthorized specifically
      if (response.status === 403) {
        await tokenErrorToast(response);
        return null;
      }

      if (response.status === 401) {
        if (typeof window !== "undefined") {
          window.dispatchEvent(new CustomEvent("auth:token-expired"));
        }

        return {
          gated: false,
          categories: [],
          reason: "Authentication required",
        };
      }

      throw new Error(`API returned status ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching gated status:", error);
    return {
      gated: false,
      categories: [],
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

export async function fetchKeepaProdutData(
  asin: string,
  country = "UK",
  vat = 1.2,
  sellerPrice = 0,
): Promise<ProductApiResponse> {
  try {
    const accessToken = getAccessToken();

    if (!accessToken) {
      return { ...mockData, summary: { ...mockData.summary, asin } };
    }

    const response = await fetch(
      `${API_BASE_URL}/kepa/get-keepa-product/${asin}?country=${country}&vat=${vat}&seller_price=${sellerPrice}`,
      {
        method: "GET",
        headers: {
          accept: "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );

    if (!response.ok) {
      if (response.status === 403) {
        await tokenErrorToast(response);
      }

      throw new Error(`API returned status ${response.status}`);
    }

    const data = await response.json();

    return data as ProductApiResponse;
  } catch (error) {
    console.error("Error fetching product data:", error);
    return { ...mockData, summary: { ...mockData.summary, asin } };
  }
}

/**
 * Fetch current AI weight settings
 */
export async function fetchAiWeights(): Promise<{
  minimum_roi: number;
  minimum_profit: number;
  monthly_sold: number;
  maximum_bsr: number;
  gated: number;
  estimated_sales?: number;
  stock_bb: number;
  amazon_in_bb: number;
  weight_roi: number;
  weight_profit: number;
  weight_estimated_sales: number;
  weight_bsr: number;
  weight_gated: number;
  weight_stock_bb: number;
  weight_amazon_in_bb: number;
}> {
  const accessToken = getAccessToken();
  if (!accessToken) {
    throw new Error("Authentication required to fetch AI weights");
  }

  const response = await fetch(`${API_BASE_URL}/weight`, {
    method: "GET",
    headers: {
      accept: "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
  });

  if (!response.ok) {
    if (response.status === 403) {
      await tokenErrorToast(response);
    }
    const errText = await response.text();
    throw new Error(
      `Failed to fetch AI weights: ${response.status} – ${errText}`,
    );
  }

  return (await response.json()) as {
    minimum_roi: number;
    minimum_profit: number;
    monthly_sold: number;
    maximum_bsr: number;
    gated: number;
    stock_bb: number;
    amazon_in_bb: number;
    weight_roi: number;
    weight_profit: number;
    weight_estimated_sales: number;
    weight_bsr: number;
    weight_gated: number;
    weight_stock_bb: number;
    weight_amazon_in_bb: number;
  };
}

/**
 * POST /weight
 */
export async function saveAiWeights(payload: {
  minimum_roi: number;
  minimum_profit: number;
  estimated_sales: number;
  maximum_bsr: number;
  gated: boolean;
  stock_bb: number;
  amazon_in_bb: number;
  weight_roi: number;
  weight_profit: number;
  weight_estimated_sales: number;
  weight_bsr: number;
  weight_gated: number;
  weight_stock_bb: number;
  weight_amazon_in_bb: number;
}): Promise<void> {
  const token = getAccessToken();
  if (!token) throw new Error("Authentication required");

  const res = await fetch(`${API_BASE_URL}/weight`, {
    method: "POST",
    headers: {
      accept: "application/json",
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(payload),
  });
  if (!res.ok) {
    const errText = await res.text();
    throw new Error(`Failed to save AI weights (${res.status}): ${errText}`);
  }
}

/**
 * Calculate product score based on product metrics
 * @returns Object containing score percentage and text value
 */
export async function calculateProductScore(params: {
  roi: number;
  profit: number;
  est_sales: number;
  bsr: number;
  gated: boolean;
  stock_level: number;
  amazon_buybox: Record<string, number> | {};
  asin_code: string;
}): Promise<{
  percentage: number;
  value: string;
  color: string; // <- ✅ Add this
}> {
  const accessToken = getAccessToken();
  if (!accessToken) {
    throw new Error("Authentication required to calculate product score");
  }

  console.log("Calculating product score with:", {
    roi: params.roi,
    profit: params.profit,
    est_sales: params.est_sales,
    bsr: params.bsr,
    gated: params.gated,
    stock_level: params.stock_level,
    amazon_buybox: params.amazon_buybox,
    asin_code: params.asin_code,
  });

  try {
    const response = await fetch(`${API_BASE_URL}/weight/score`, {
      method: "POST",
      headers: {
        accept: "application/json",
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
      body: JSON.stringify({
        roi: params.roi,
        profit: params.profit,
        est_sales: params.est_sales,
        bsr: params.bsr,
        gated: params.gated,
        stock_level: params.stock_level,
        amazon_buybox: params.amazon_buybox,
        asin_code: params.asin_code,
      }),
    });

    if (!response.ok) {
      const errText = await response.text();
      if (response.status === 403) {
        await tokenErrorToast(response);
      }
      throw new Error(
        `Failed to calculate product score: ${response.status} – ${errText}`,
      );
    }

    const result = await response.json();
    return result; // Returns { percentage: 0.75, value: "Poor" }
  } catch (error) {
    console.error("Error calculating product score:", error);
    // Return a default response if the API call fails
    return {
      percentage: 0.5,
      value: "Average",
      color: "#facc15",
    };
  }
}

/**
 * Interface for AI chat conversation history
 */
export interface ConversationHistoryItem {
  ai_message: string;
  human_message: string;
}

/**
 * Interface for AI chat request
 */
export interface AIChatRequest {
  user_query: string;
  conversation_history: ConversationHistoryItem[];
  stream: boolean;
  product_data?: ProductData;
  asin_code: string; // Add asin_code property
}

/**
 * Analyze product using AI chat API with streaming support
 * @param userQuery User's question/query
 * @param conversationHistory Previous conversation history
 * @param productData Current product information for context
 * @returns AsyncGenerator for streaming response or regular response
 */
export async function analyzeProductWithAI(
  userQuery: string,
  conversationHistory: ConversationHistoryItem[] = [],
  productData?: ProductData,
  stream: boolean = true,
): Promise<AsyncGenerator<string> | { response: string }> {
  try {
    const accessToken = getAccessToken();

    if (!accessToken) {
      throw new Error("Authentication required for AI analysis");
    }

    // Prepare the request payload
    const payload: AIChatRequest = {
      user_query: userQuery,
      conversation_history: conversationHistory,
      stream: stream,
      product_data: productData,
      asin_code: productData?.asin ?? "",
    };

    const response = await fetch(
      "https://app.clickbuy.ai/ai-chat/api/v1/checkora/analyze",
      {
        method: "POST",
        headers: {
          accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(payload),
      },
    );

    if (!response.ok) {
      if (response.status === 403) {
        await tokenErrorToast(response);
      }
      if (response.status === 401) {
        // Token expired
        if (typeof window !== "undefined") {
          window.dispatchEvent(new CustomEvent("auth:token-expired"));
        }
        throw new Error("Authentication expired");
      }

      const errorText = await response.text();
      throw new Error(`AI API error: ${response.status} - ${errorText}`);
    }

    if (stream) {
      // Return streaming generator
      return streamResponse(response);
    } else {
      // Return regular JSON response
      const data = await response.json();
      return { response: data.response || "No response received" };
    }
  } catch (error) {
    console.error("Error in AI analysis:", error);
    throw error;
  }
}

/**
 * Generator function to handle streaming response
 */
async function* streamResponse(response: Response): AsyncGenerator<string> {
  const reader = response.body?.getReader();
  const decoder = new TextDecoder();

  if (!reader) {
    throw new Error("No response body reader available");
  }

  try {
    while (true) {
      const { done, value } = await reader.read();

      if (done) {
        break;
      }

      // Decode the chunk
      const chunk = decoder.decode(value, { stream: true });

      // Split by lines in case multiple chunks come together
      const lines = chunk.split("\n");

      for (const line of lines) {
        if (line.trim()) {
          // Handle Server-Sent Events format if needed
          if (line.startsWith("data: ")) {
            const data = line.substring(6);
            if (data === "[DONE]") {
              return;
            }
            try {
              const parsed = JSON.parse(data);
              if (parsed.token) {
                yield parsed.token;
              } else if (typeof parsed === "string") {
                yield parsed;
              }
            } catch (e) {
              // If not JSON, yield as plain text
              yield data;
            }
          } else {
            // Plain text streaming
            yield line;
          }
        }
      }
    }
  } finally {
    reader.releaseLock();
  }
}

/**
 * Helper function to build conversation history from messages
 */
export function buildConversationHistory(
  messages: Array<{
    text: string;
    sender: "user" | "bot";
    isStreaming?: boolean; // optional – ignored if absent
  }>,
): ConversationHistoryItem[] {
  const history: ConversationHistoryItem[] = [];
  let pendingUser: string | null = null;

  for (const msg of messages) {
    if (msg.sender === "user") {
      pendingUser = msg.text; // remember latest user line
    } else if (
      msg.sender === "bot" &&
      !msg.isStreaming && // skip streaming placeholders
      pendingUser !== null // only pair if we have a user line
    ) {
      history.push({
        human_message: pendingUser,
        ai_message: msg.text,
      });
      pendingUser = null; // reset for next pair
    }
  }

  return history;
}
