"use client";
import { useAuth } from "@/hooks/useAuth";
import { UserCircle, X } from "lucide-react";
import { useState, useEffect } from "react";
import { LoginModal } from "../auth/LoginModal";
import { storageService } from "@/lib/api/storage";
import { AUTH_COOKIES } from "@/lib/utils/cookie";

const Nav = () => {
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const { user, setUser, setToken } = useAuth();

  // useEffect(() => {
  //   setMounted(true);
  // }, [user]);

  // if (!mounted) {
  //   return null;
  // }

  const logout = () => {
    try {
      storageService.removeItem(AUTH_COOKIES.ACCESS_TOKEN);
      storageService.removeItem(AUTH_COOKIES.REFRESH_TOKEN);
      storageService.removeItem(AUTH_COOKIES.USER);

      setUser(null);
      setToken(null);

      return { success: true };
    } catch (error) {
      console.error("Logout error:", error);
      return { success: false, error: "Failed to logout" };
    }
  };

  return (
    <div className="flex items-center justify-end bg-black px-2 py-1">
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
      />
      <div className="flex items-center gap-1">
        {user && user.isLoggedIn ? (
          <div className="flex items-center">
            <div className="mr-1 flex items-center text-xs bg-gray-800 px-1 py-0.5 rounded">
              <UserCircle size={12} className="mr-1 text-white" />
              <span className="truncate max-w-[100px] text-white">
                {user.email || ""}
              </span>
            </div>
            <button
              onClick={logout}
              className="text-xs hover:underline text-white"
            >
              Logout
            </button>
          </div>
        ) : (
          <button
            onClick={() => setIsLoginModalOpen(true)}
            className="text-xs hover:underline mr-1 text-white"
          >
            Login
          </button>
        )}

        <button
          onClick={() => {}}
          className="rounded p-0.5 text-xs text-white hover:bg-gray-700 transition-colors"
          title="Switch Side"
        >
          Switch
        </button>
        <button
          onClick={() => {}}
          className="rounded-full p-0.5 transition-colors hover:bg-gray-700"
        >
          <X size={14} className="text-white" />
        </button>
      </div>
    </div>
  );
};

export default Nav;
