import { useAuth } from "@/hooks/useAuth";
import { useProductContext } from "@/hooks/useProduct";
import { OfferItem } from "@/lib/types/home";
import { formatToCurrency } from "@/lib/utils/index";
import { useState } from "react";

const Offers = () => {
  const [isOffersExpanded, setIsOffersExpanded] = useState(false);

  const { user } = useAuth();
  const { productData } = useProductContext();

  // console.log("productData", productData);

  const getDetailedOffers = (): OfferItem[] => {
    try {
      if (!productData?.offers?.sellers_offers) {
        return [];
      }

      return productData.offers.sellers_offers.map((offer, index) => ({
        position: index + 1,
        // Cast to any to avoid type checking on this property
        type: (offer.is_amazon ? "AMZ" : offer.is_fba ? "FBA" : "FBM") as any,
        stock: offer.stock,
        price: formatToCurrency(offer.price),
      }));
    } catch (err) {
      console.error("Error getting detailed offers:", err);
      //   updateSectionError(
      //     "offers",
      //     "Could not process offer details. Showing limited information.",
      //   );
      return [];
    }
  };

  const detailedOffers = getDetailedOffers();

  return (
    <div>
      <>
        {/* Total and breakdown */}
        <div className="flex justify-between items-center mb-2 p-2">
          <span className="text-xs font-bold">
            Total: {(productData && productData?.offers?.total) || 0}
          </span>
          <div className="flex gap-2 text-xs">
            <span>
              <span className="text-green-500 text-md">●</span> FBA:{" "}
              {(productData && productData.offers?.fba) || 0}
            </span>
            <span>
              <span className="text-yellow-500 text-md">●</span> FBM:{" "}
              {(productData && productData.offers?.fbm) || 0}
            </span>
          </div>
        </div>

        {/* Offers table */}
        <table className="w-full text-xs border rounded-lg">
          <thead>
            <tr className="bg-gray-100">
              <th className="p-1 text-center border">#</th>
              <th className="p-1 text-center border">Seller</th>
              <th className="p-1 text-center border">Stock</th>
              <th className="p-1 text-center border">Price</th>
            </tr>
          </thead>
          <tbody>
            {user && detailedOffers.length > 0 ? (
              <>
                {detailedOffers
                  .slice(0, isOffersExpanded ? detailedOffers.length : 5)
                  .map((offer) => (
                    <tr key={offer.position}>
                      <td className="p-1 text-center border">
                        {offer.position}
                      </td>
                      <td className="p-1 text-center border">
                        <span
                          className={`px-1 rounded ${
                            (offer.type as any) === "AMZ"
                              ? "bg-purple-100 text-purple-800"
                              : offer.type === "FBA"
                                ? "bg-green-100 text-green-800"
                                : "bg-yellow-100 text-yellow-800"
                          }`}
                        >
                          {offer.type}
                        </span>
                      </td>
                      <td className="p-1 text-center border">{offer.stock}</td>
                      <td className="p-1 text-center border">{offer.price}</td>
                    </tr>
                  ))}
              </>
            ) : (
              <tr>
                <td
                  colSpan={4}
                  className="p-1 text-center text-gray-500 text-xs border"
                >
                  {user ? "No offers data available" : "Login to view offers"}
                </td>
              </tr>
            )}
          </tbody>
        </table>

        {/* Show All button */}
        {detailedOffers.length > 5 && (
          <div className="text-center mt-2">
            <button
              onClick={() => setIsOffersExpanded(!isOffersExpanded)}
              className="text-xs text-gray-600 hover:underline"
            >
              {isOffersExpanded
                ? "Show Less"
                : `Show All (${detailedOffers.length})`}
            </button>
          </div>
        )}
      </>
    </div>
  );
};

export default Offers;
