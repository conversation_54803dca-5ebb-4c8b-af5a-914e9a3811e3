"use client";
import Image from "next/image";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { Box, List, Lock, TrendingUp } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { LoginModal } from "../auth/LoginModal";
import { useEffect, useState } from "react";
import {
  formatToCurrency,
  formatWithCommas,
  getCurrencySymbol,
  getCurrentProfit,
  getCurrentROI,
} from "@/lib/utils/index";
import { useProductContext } from "@/hooks/useProduct";

const Overview = () => {
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [inputPurchasePrice, setInputPurchasePrice] = useState("");
  const [inputBuyBoxPrice, setInputBuyBoxPrice] = useState("");

  const { user, setUser, setToken } = useAuth();
  const { productData, setProductData, selectedCountry, isVatRegistered } =
    useProductContext();

  const handlePurchasePriceChange = (value: string) => {
    setInputPurchasePrice(value);
  };

  const handleBuyBoxPriceChange = (value: string) => {
    setInputBuyBoxPrice(value);
  };

  return (
    <div className="border p-2 border-black mx-1">
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
      />
      <div className="flex gap-2 mb-2">
        <div className="w-1/4 h-fit border p-1 rounded-md">
          <div className="relative aspect-square">
            <Image
              src={
                (productData && productData.mainImage) ||
                "/api/placeholder/200/200"
              }
              alt={(productData && productData.title) || "Amazon Product"}
              width={200}
              height={200}
              className="object-contain w-full h-full"
            />
          </div>
        </div>

        <div className="w-3/4">
          <div className="whitespace-normal break-words text-xs font-bold pb-1">
            {(productData && productData.title) ||
              "Product Title Not Available"}
          </div>

          <div className="border-b border-gray-200 my-1"></div>

          {/* Secondary info */}
          <div className="grid grid-cols-2 gap-x-2 gap-y-0 text-[11px] mb-2">
            <div>
              <span className="font-medium">ASIN:</span>{" "}
              {(productData && productData.asin) || "N/A"}
            </div>
            <div>
              <span className="font-medium">EAN:</span>{" "}
              {productData &&
              productData.summary &&
              productData.summary.ean !== "Not available without API"
                ? productData?.summary?.ean || "N/A"
                : "N/A"}
            </div>
            <div>
              <span className="font-medium">Brand:</span>{" "}
              {(productData && productData.brand) || "N/A"}
            </div>
            <div>
              <span className="font-medium">Rating:</span>{" "}
              {productData && productData.rating
                ? `${parseFloat(productData.rating.split(" ")[0]).toFixed(2)} (${productData.reviewCount || 0})`
                : "0.00 (0)"}
            </div>
          </div>

          {/* Buttons for Product Details */}
          <div className="flex flex-wrap gap-1 mt-1 mb-1">
            <div className="relative inline-block">
              <Popover>
                <PopoverTrigger asChild>
                  <button className="flex items-center bg-gray-100 px-1.5 py-0.5 rounded text-xs">
                    <Box size={10} className="mr-0.5" />
                    Dimensions
                  </button>
                </PopoverTrigger>
                <PopoverContent
                  className="border border-black bg-white w-[220px] p-2 text-xs"
                  align="start"
                >
                  <p className="mb-0.5">
                    <strong>Dimensions:</strong>
                  </p>
                  <ul className="list-none">
                    <li>
                      <strong>Height:</strong>{" "}
                      {(productData &&
                        productData.dimensions &&
                        productData.dimensions.height.value) ||
                        "0"}{" "}
                      {(productData &&
                        productData.dimensions &&
                        productData.dimensions.height.unit) ||
                        ""}
                    </li>
                    <li>
                      <strong>Length:</strong>{" "}
                      {(productData &&
                        productData.dimensions &&
                        productData.dimensions.length.value) ||
                        "0"}{" "}
                      {(productData &&
                        productData.dimensions &&
                        productData.dimensions.length.unit) ||
                        ""}
                    </li>
                    <li>
                      <strong>Width:</strong>{" "}
                      {(productData &&
                        productData.dimensions &&
                        productData.dimensions.width.value) ||
                        "0"}{" "}
                      {(productData &&
                        productData.dimensions &&
                        productData.dimensions.width.unit) ||
                        ""}
                    </li>
                    <li>
                      <strong>Weight:</strong>{" "}
                      {(productData &&
                        productData.dimensions &&
                        productData.dimensions.weight.value) ||
                        "0"}{" "}
                      {(productData &&
                        productData.dimensions &&
                        productData.dimensions.weight.unit) ||
                        ""}
                    </li>
                  </ul>
                </PopoverContent>
              </Popover>
            </div>

            <div className="relative inline-block">
              <Popover>
                <PopoverTrigger asChild>
                  <button className="flex items-center bg-gray-100 px-1.5 py-0.5 rounded text-xs">
                    <List size={10} className="mr-0.5" />
                    Features
                  </button>
                </PopoverTrigger>
                <PopoverContent
                  className="border border-black bg-white w-[250px] p-2 text-black"
                  align="end"
                >
                  <div className="max-h-[200px] overflow-y-auto py-1">
                    {productData &&
                    productData.features &&
                    productData.features.length > 0 ? (
                      <ul className="list-disc pl-4 space-y-1">
                        {productData.features.map((feature, index) => (
                          <li key={index} className="text-xs">
                            {feature}
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <p className="text-xs text-gray-300">
                        No feature information available
                      </p>
                    )}
                  </div>
                </PopoverContent>
              </Popover>
            </div>

            {/* Links Popover */}
            <div className="relative inline-block">
              <Popover>
                <PopoverTrigger asChild>
                  <button className="flex items-center bg-gray-100 px-1.5 py-0.5 rounded text-xs">
                    <Box size={10} className="mr-0.5" />
                    Links
                  </button>
                </PopoverTrigger>
                <PopoverContent
                  className="border border-black bg-white w-[220px] p-2"
                  align="end"
                >
                  <div className="flex flex-col gap-1">
                    {/* {apiData?.summary &&
                        (apiData.summary as any)
                        ?.ebay_active_listing_url ? (
                        <a
                            href={
                            (apiData?.summary as any)
                                ?.ebay_active_listing_url
                            }
                            target="_blank"
                            rel="noopener noreferrer"
                        >
                            <button className="w-full bg-gray-100 text-gray-800 hover:bg-gray-200 px-1.5 py-0.5 rounded text-xs font-medium">
                            eBay Live
                            </button>
                        </a>
                        ) : (
                        <button
                            disabled
                            className="w-full bg-gray-100 text-gray-400 px-1.5 py-0.5 rounded text-xs font-medium"
                        >
                            eBay Live
                        </button>
                        )}

                        {apiData?.summary &&
                        (apiData.summary as any)
                        ?.ebay_sold_listing_url ? (
                        <a
                            href={
                            (apiData?.summary as any)
                                ?.ebay_sold_listing_url
                            }
                            target="_blank"
                            rel="noopener noreferrer"
                        >
                            <button className="w-full bg-green-100 text-green-800 hover:bg-green-200 px-1.5 py-0.5 rounded text-xs font-medium">
                            eBay Sold
                            </button>
                        </a>
                        ) : (
                        <button
                            disabled
                            className="w-full bg-gray-100 text-gray-400 px-1.5 py-0.5 rounded text-xs font-medium"
                        >
                            eBay Sold
                        </button>
                        )} */}

                    {/* {apiData?.summary &&
                        (apiData.summary as any)?.hagglezon_url ? (
                        <a
                            href={
                            (apiData?.summary as any)?.hagglezon_url
                            }
                            target="_blank"
                            rel="noopener noreferrer"
                        >
                            <button className="w-full bg-yellow-100 text-yellow-800 hover:bg-yellow-200 px-1.5 py-0.5 rounded text-xs font-medium">
                            Hagglezon
                            </button>
                        </a>
                        ) : (
                        <button
                            disabled
                            className="w-full bg-gray-100 text-gray-400 px-1.5 py-0.5 rounded text-xs font-medium"
                        >
                            Hagglezon
                        </button>
                        )} */}

                    {/* <a
                        href={`https://globalesearch.com/?searchTerm=${apiData?.summary?.ean?.[0] || productInfo.asin}&lt=1&lt=2&sortBy=price&category=-1&searchInDescription=false&se=0&se=${getGlobalESearchParams()}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        >
                        <button className="w-full bg-purple-100 text-purple-800 hover:bg-purple-200 px-1.5 py-0.5 rounded text-xs font-medium">
                            Global
                        </button>
                        </a> */}
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>
      </div>
      <div className="grid grid-cols-5 gap-1 mb-1">
        <div className="border border-black rounded p-1 flex flex-col items-center bg-gray-100">
          <div className="font-bold text-[10px]">Buy Price</div>
          <div className="relative w-full mt-2">
            {user && user.isLoggedIn ? (
              <div className="flex items-center border border-gray-400 rounded focus:ring-1 focus:ring-black focus:border-black gap-1 overflow-hidden">
                <span className=" text-xs inset-y-0 left-0 flex items-center pl-1 text-gray-500">
                  {getCurrencySymbol(selectedCountry)}
                </span>
                <input
                  type="text"
                  value={inputPurchasePrice}
                  onChange={(e) => {
                    const value = e.target.value.replace(/[^0-9.]/g, "");
                    const parts = value.split(".");
                    const formattedValue =
                      parts.length > 2
                        ? parts[0] + "." + parts.slice(1).join("")
                        : value;
                    handlePurchasePriceChange(formattedValue);
                  }}
                  className="w-full py-0 text-sm font-bold text-center"
                />
                {/* {isCalculating && (
                    <span className="absolute inset-y-0 right-1 flex items-center">
                        <div className="h-3 w-3 animate-spin rounded-full border-2 border-black border-t-transparent"></div>
                    </span>
                    )} */}
              </div>
            ) : (
              <div className="group relative w-full">
                <button
                  onClick={() => setIsLoginModalOpen(true)}
                  className="w-full py-0.5 text-center border rounded bg-gray-100 flex items-center justify-center"
                >
                  <Lock size={14} className="text-gray-500" />
                </button>
                <div className="invisible absolute top-0 right-0 translate-x-full ml-1 px-2 py-1 text-xs bg-gray-800 text-white rounded opacity-0 transition-opacity group-hover:visible group-hover:opacity-100 z-50 whitespace-nowrap">
                  Login to use calculator
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="border border-black rounded p-1 flex flex-col items-center bg-gray-100">
          <div className="font-bold text-[10px]">Sells price</div>
          <div className="relative w-full mt-2">
            {user && user.isLoggedIn ? (
              <div className="flex items-center border border-gray-400 rounded focus:ring-1 focus:ring-black focus:border-black gap-1 overflow-hidden">
                <span className="text-xs inset-y-0 left-0 flex items-center pl-1 text-gray-500">
                  {getCurrencySymbol(selectedCountry)}
                </span>
                <input
                  type="text"
                  value={inputBuyBoxPrice}
                  onChange={(e) => {
                    const value = e.target.value.replace(/[^0-9.]/g, "");
                    const parts = value.split(".");
                    const formattedValue =
                      parts.length > 2
                        ? parts[0] + "." + parts.slice(1).join("")
                        : value;
                    handleBuyBoxPriceChange(formattedValue);
                  }}
                  className="w-full py-0 text-sm font-bold text-center"
                />
                {/* {isCalculating && (
                    <span className="absolute inset-y-0 right-1 flex items-center">
                        <div className="h-3 w-3 animate-spin rounded-full border-2 border-black border-t-transparent"></div>
                    </span>
                    )} */}
              </div>
            ) : (
              <div className="group relative w-full">
                <button
                  onClick={() => setIsLoginModalOpen(true)}
                  className="w-full py-0.5 text-center border rounded bg-gray-100 flex items-center justify-center"
                >
                  <Lock size={14} className="text-gray-500" />
                </button>
                <div className="invisible absolute top-0 right-0 translate-x-full ml-1 px-2 py-1 text-xs bg-gray-800 text-white rounded opacity-0 transition-opacity group-hover:visible group-hover:opacity-100 z-50 whitespace-nowrap">
                  Login to use calculator
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="border border-black rounded p-1 flex flex-col items-center bg-gray-100 justify-between">
          <div className="font-bold text-[10px]">BSR</div>
          <div className="text-md font-bold mb-[2px]">
            {formatWithCommas(
              typeof productData?.bsr === "string"
                ? parseFloat(productData?.bsr)
                : productData?.bsr,
            ) ||
              formatWithCommas(
                productData?.pricing?.non_vat_pricing?.sales_rank,
              ) ||
              "N/A"}
          </div>
        </div>

        <div className="border border-black rounded p-1 flex flex-col items-center bg-gray-100 justify-between">
          <div className="font-bold text-[10px] flex items-center">
            <TrendingUp size={10} className="mr-0.5" />
            ROI
          </div>
          <div
            className={`text-md font-bold mb-[2px] ${productData && getCurrentROI({ isVatRegistered, productData }) > 0 ? "text-green-600" : "text-red-600"}`}
          >
            {productData &&
              getCurrentROI({ isVatRegistered, productData }).toFixed(1)}
            %
          </div>
        </div>

        <div className="border border-black rounded p-1 flex flex-col items-center bg-gray-100 justify-between">
          <div className="font-bold text-[10px] flex items-center">Profit</div>
          <div
            className={`text-md font-bold mb-[2px] ${productData && getCurrentProfit({ isVatRegistered, productData }) > 0 ? "text-green-600" : "text-red-600"}`}
          >
            {productData &&
              formatToCurrency(
                getCurrentProfit({ isVatRegistered, productData }),
              )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Overview;
