import { useProductContext } from "@/hooks/useProduct";

const WarningAndRestrictions = () => {
  const { productData, isGated } = useProductContext();

  return (
    <div className="grid grid-cols-3 gap-2 p-2">
      <div className="bg-gray-100 border border-gray-400 rounded py-[0.5px]  px-1 flex items-center justify-between">
        <span className="text-[9.5px] font-bold whitespace-nowrap">
          Amazon in Buy Box:
        </span>
        <span
          className={`text-[9.5px] font-bold ${
            productData && productData.warnings?.amz_in_buy_box
              ? "text-red-600"
              : "text-green-600"
          }`}
        >
          {productData && productData.warnings?.amz_in_buy_box ? "Yes" : "No"}
        </span>
      </div>

      {/* Adult Product */}
      <div className="bg-gray-100 border border-gray-400 rounded py-[0.5px]  px-1 flex items-center justify-between">
        <span className="text-[9.5px] font-bold whitespace-nowrap">
          Adult Product:
        </span>
        <span
          className={`text-[9.5px] font-bold ${
            productData && productData.warnings?.adult_product
              ? "text-red-600"
              : "text-green-600"
          }`}
        >
          {productData && productData.warnings?.adult_product ? "Yes" : "No"}
        </span>
      </div>

      {/* Meltable Product */}
      <div className="bg-gray-100 border border-gray-400 rounded py-[0.5px]  px-1 flex items-center justify-between">
        <span className="text-[9.5px] font-bold whitespace-nowrap">
          Meltable Product:
        </span>
        <span
          className={`text-[9.5px] font-bold ${
            productData && productData.warnings?.meltable
              ? "text-red-600"
              : "text-green-600"
          }`}
        >
          {productData && productData.warnings?.meltable ? "Yes" : "No"}
        </span>
      </div>

      {/* Has Variations */}
      <div className="bg-gray-100 border border-gray-400 rounded py-[0.5px]  px-1 flex items-center justify-between">
        <span className="text-[9.5px] font-bold whitespace-nowrap">
          Has Variations:
        </span>
        <span
          className={`text-[9.5px] font-bold ${
            productData &&
            productData.warnings?.variations?.is_variations &&
            productData &&
            productData.warnings?.variations?.variations_value?.length > 0
              ? "text-red-600"
              : "text-green-600"
          }`}
        >
          {productData &&
          productData.warnings?.variations?.is_variations &&
          productData &&
          productData.warnings?.variations?.variations_value?.length > 0
            ? "Yes"
            : "No"}
        </span>
      </div>

      {/* Category Gated */}
      <div className="bg-gray-100 border border-gray-400 rounded py-[0.5px]  px-1 flex items-center justify-between">
        <span className="text-[9.5px] font-bold whitespace-nowrap">
          Category Gated:
        </span>
        <span
          className={`text-[9.5px] font-bold ${
            isGated ? "text-red-600" : "text-green-600"
          }`}
        >
          {isGated ? "Yes" : "No"}
        </span>
      </div>

      {/* Private Label */}
      <div className="bg-gray-100 border border-gray-400 rounded py-[0.5px]  px-1 flex items-center justify-between">
        <span className="text-[9.5px] font-bold whitespace-nowrap">
          Private Label:
        </span>
        <span className="text-[9.5px] font-bold text-green-600">No</span>
      </div>
    </div>
  );
};

export default WarningAndRestrictions;
