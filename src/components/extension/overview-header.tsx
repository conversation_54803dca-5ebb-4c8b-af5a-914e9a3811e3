"use client";
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw } from "lucide-react";
import { Button } from "../ui/button";
import { LoginModal } from "../auth/LoginModal";
import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";

const OverViewHeader = () => {
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [mounted, setMounted] = useState(false);

  const { user, setUser, setToken } = useAuth();

  return (
    <div>
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
      />
      <div className="relative mx-1 mt-1 bg-black border border-black text-green-500 px-2 py-1 flex items-center justify-center">
        <span className="text-sm font-extrabold tracking-wide">OVERVIEW</span>

        {false && (
          <div className="absolute left-2 flex items-center">
            <div className="h-3 w-3 animate-spin rounded-full border border-green-500 border-t-transparent mr-1" />
            <span className="text-xs text-green-400">Loading details...</span>
          </div>
        )}

        <div className="absolute right-2 flex items-center gap-2">
          <Button
            disabled={!user?.isLoggedIn}
            className="bg-gray-800 hover:bg-gray-700 p-0.5 rounded text-green-500 h-fit"
            title="Refresh Data"
          >
            <RefreshCw size={14} />
          </Button>
        </div>
      </div>
      {!user && mounted && (
        <div className="bg-yellow-50 border border-yellow-200 mx-1 rounded px-1 py-1 flex items-center space-x-1">
          <AlertTriangle size={12} className="text-yellow-500" />
          <div className="text-xs text-yellow-700 flex-1">
            Login for enhanced data
          </div>
          <Button
            onClick={() => setIsLoginModalOpen(true)}
            className="bg-black text-white px-1.5 py-0.5 rounded text-xs hover:bg-gray-800"
          >
            Login
          </Button>
        </div>
      )}
    </div>
  );
};

export default OverViewHeader;
