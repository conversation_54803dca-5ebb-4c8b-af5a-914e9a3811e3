import React, { useState } from "react";
import {
  Lock,
  Info,
  Calendar,
  TrendingUp,
  User,
  AlertTriangle,
} from "lucide-react";
import BuyBoxChart from "../charts/BuyBoxChart";
import { useAuth } from "@/hooks/useAuth";
import { useProductContext } from "@/hooks/useProduct";
import { getCurrencySymbol, getCurrentBuyBoxPrice } from "@/lib/utils/index";
import { useGetKeepaData } from "@/hooks/useGetKeepaData";
import { LoginModal } from "../auth/LoginModal";

// Define types for the props
interface BuyBoxHistorySectionProps {
  isAuthenticated: boolean;
  buyBoxHistory?: BuyBoxHistoryData;
  onLoginClick: () => void;
  country?: string; // Add country prop
}

// Define types for the BuyBoxHistory data
interface BuyBoxHistoryData {
  "30_days"?: Record<string, number>;
  "90_days"?: Record<string, number>;
  "180_days"?: Record<string, number>;
  "365_days"?: Record<string, number>;
}

// Define type for insights
interface BuyBoxInsights {
  totalSellers: number;
  topSeller: string;
  topSellerPercent: number;
  amazonPercent: number;
  totalDays: number;
}

const BuyBoxHistory = () => {
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);

  const { user, setUser, setToken } = useAuth();
  const { productData, setProductData, selectedCountry, isVatRegistered } =
    useProductContext();
  const {
    data: apiData,
    isLoading,
    error,
  } = useGetKeepaData({
    KeepaRequest: {
      asin: productData?.asin || "",
      country: selectedCountry || "GB",
      vat: 1.2,
      sellerPrice:
        (productData &&
          getCurrentBuyBoxPrice({
            isVatRegistered: isVatRegistered,
            productData: productData,
          })) ||
        0,
    },
  });

  const buyBoxHistory = apiData?.buy_box_history;

  // Get today's date in a nice format
  const today = new Date().toLocaleDateString("en-GB", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  // Format currency with dynamic currency symbol
  const formatCurrency = (value: number | undefined | null): string => {
    try {
      if (value === undefined || value === null)
        return `${getCurrencySymbol(selectedCountry)}0.00`;
      return `${getCurrencySymbol(selectedCountry)}${value.toFixed(2)}`;
    } catch (err) {
      console.error("Error formatting currency:", err);
      return `${getCurrencySymbol(selectedCountry)}0.00`;
    }
  };

  // Extract insights from buy box history if available
  const getInsights = (): BuyBoxInsights | null => {
    try {
      if (!buyBoxHistory) return null;

      // Use 30 days by default, fall back to longest available period
      const period: Record<string, number> =
        buyBoxHistory["30_days"] ||
        buyBoxHistory["90_days"] ||
        buyBoxHistory["180_days"] ||
        buyBoxHistory["365_days"] ||
        {};

      if (Object.keys(period).length === 0) return null;

      // Find most frequent seller
      let topSeller: { id: string; days: number } = { id: "None", days: 0 };
      let amazonDays = 0;
      let totalDays = 0;

      Object.entries(period).forEach(([id, days]: [string, number]) => {
        totalDays += days;

        // Check if this is Amazon (could be A3P5ROKL5A1OLE or similar)
        if (id.includes("A3P5ROKL5A1OLE") || id === "Amazon") {
          amazonDays += days;
        }

        if (days > topSeller.days) {
          topSeller = { id, days };
        }
      });

      // Calculate percentages
      const topSellerPercent =
        totalDays > 0 ? Math.round((topSeller.days / totalDays) * 100) : 0;
      const amazonPercent =
        totalDays > 0 ? Math.round((amazonDays / totalDays) * 100) : 0;

      return {
        totalSellers: Object.keys(period).length,
        topSeller: topSeller.id,
        topSellerPercent,
        amazonPercent,
        totalDays,
      };
    } catch (err) {
      console.error("Error processing buy box insights:", err);
      //   setError("Could not load buy box insights. ClickBuy is updating.");
      return null;
    }
  };

  // Try to get insights safely
  let insights = null;
  try {
    if (user?.isLoggedIn) {
      insights = getInsights();
    }
  } catch (err) {
    console.error("Error in insights calculation:", err);
    // setError("Could not process buy box data. ClickBuy is updating.");
  }

  // Function to retry when error occurs
  const handleRetry = () => {
    // setError(null);
    try {
      if (user?.isLoggedIn) {
        insights = getInsights();
      }
    } catch (err) {
      console.error("Error during retry:", err);
      //   setError("Still updating. Please try again later.");
    }
  };

  return (
    <div className=" overflow-hidden bg-white shadow">
      {/* <div className="bg-black text-green-500 px-2 py-1  flex items-center justify-center">
        <span className="text-sm font-bold">BUY BOX HISTORY</span>
        {error && <div className="text-xs text-yellow-700 ml-2">{error}</div>}
      </div> */}
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
      />
      <div className="p-2">
        {error ? (
          // Error state
          <div className="flex items-center justify-center p-4 bg-yellow-50 rounded border border-yellow-200">
            <AlertTriangle size={16} className="text-yellow-500 mr-2" />
            <div className="text-sm text-yellow-700">
              {/* {error} */}
              <button
                className="ml-2 underline text-gray-600"
                onClick={handleRetry}
              >
                Try Again
              </button>
            </div>
          </div>
        ) : user?.isLoggedIn && buyBoxHistory ? (
          <>
            {/* Pass country prop to BuyBoxChart if it accepts it */}
            <BuyBoxChart
              buyBoxHistory={buyBoxHistory}
              country={selectedCountry} // Pass country to chart component
            />

            {/* Additional data elements */}
            <div className="mt-3 border-t pt-2">
              <div className="grid grid-cols-2 gap-2">
                {/* Date */}
                <div className="flex items-center bg-gray-50 p-1.5 rounded">
                  <Calendar size={14} className="text-gray-700 mr-1.5" />
                  <div>
                    <div className="text-xs text-gray-700">Today's Date</div>
                    <div className="text-xs font-medium">{today}</div>
                  </div>
                </div>

                {/* Number of Sellers */}
                <div className="flex items-center bg-gray-50 p-1.5 rounded">
                  <User size={14} className="text-gray-700 mr-1.5" />
                  <div>
                    <div className="text-xs text-gray-700">
                      Unique Sellers (30d)
                    </div>
                    <div className="text-xs font-medium">
                      {insights?.totalSellers || "N/A"}
                    </div>
                  </div>
                </div>

                {/* Top Seller */}
                <div className="flex items-center bg-gray-50 p-1.5 rounded">
                  <TrendingUp size={14} className="text-gray-700 mr-1.5" />
                  <div>
                    <div className="text-xs text-gray-700">
                      Top Buy Box Holder
                    </div>
                    <div className="text-xs font-medium">
                      {insights
                        ? `${insights.topSeller} (${insights.topSellerPercent}%)`
                        : "N/A"}
                    </div>
                  </div>
                </div>

                {/* Amazon Percentage */}
                <div className="flex items-center bg-gray-50 p-1.5 rounded">
                  <Info size={14} className="text-gray-700 mr-1.5" />
                  <div>
                    <div className="text-xs text-gray-700">
                      Amazon in Buy Box
                    </div>
                    <div className="text-xs font-medium">
                      {insights ? `${insights.amazonPercent}% of time` : "N/A"}
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-2 text-xs text-gray-500 italic text-center">
                Note: Buy Box history shows seller distribution over time
                periods
              </div>
            </div>
          </>
        ) : user?.isLoggedIn ? (
          // Authenticated but no data - show ClickBuy is updating message
          <div className="flex flex-col items-center justify-center p-4">
            <Info className="h-5 w-5 text-gray-500 mb-1" />
            <p className="text-center text-xs text-gray-700">
              ClickBuy is updating.
            </p>
          </div>
        ) : (
          // Not authenticated - show login prompt
          <div className="flex flex-col items-center justify-center p-4 relative">
            <div className="absolute inset-0 bg-gray-100 blur-sm flex items-center justify-center opacity-50">
              <div className="w-full h-16 bg-gray-200"></div>
            </div>
            <div className="z-10 flex flex-col items-center">
              <Lock className="h-5 w-5 text-gray-500 mb-1" />
              <p className="text-center text-xs text-gray-700">
                Login to view this data
              </p>
              <button
                className="mt-1 bg-gray-600 text-white px-2 py-0.5 rounded text-xs hover:bg-gray-700"
                onClick={() => setIsLoginModalOpen(true)}
              >
                Login
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BuyBoxHistory;
