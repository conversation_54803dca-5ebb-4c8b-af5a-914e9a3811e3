// Updated AISummarizer with dummy design

import React, { useState, useEffect } from "react";
import {
  Alert<PERSON><PERSON>gle,
  TrendingUp,
  CheckCircle2,
  Lock,
  Star,
  BarChart,
  Loader,
  Wand2,
  BotIcon,
  Check,
  <PERSON>,
  Wan<PERSON>,
  <PERSON><PERSON>,
} from "lucide-react";
import { calculateProductScore } from "@/lib/api/productApi";
import ChatBot from "./ChatBot";
import { ProductData, ProfitData } from "@/lib/types/home";
import { useAuth } from "@/hooks/useAuth";
import { Button } from "../ui/button";
import { LoginModal } from "../auth/LoginModal";
import { useProductContext } from "@/hooks/useProduct";
import {
  formatEstimatedSales,
  getCurrentProfit,
  getCurrentROI,
} from "@/lib/utils/index";
import { useScoreCalculator } from "@/hooks/useScoreCalculator";

const AISummarizer = () => {
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [mounted, setMounted] = useState(false);

  const { user } = useAuth();
  const { productData, isGated } = useProductContext();

  const { data: scoreData, isLoading } = useScoreCalculator({
    ScoreRequest: {
      amazon_in_bb: productData?.warnings?.amz_in_buy_box || false,
      asin_code: productData?.asin || "",
      bsr: Number(productData?.bsr) || 0,
      est_sales: productData?.estimated_sales || 0,
      gated: isGated || false,
      profit:
        (productData &&
          getCurrentProfit({
            isVatRegistered: true,
            productData: productData,
          })) ||
        0,
      roi:
        (productData &&
          getCurrentROI({ isVatRegistered: true, productData: productData })) ||
        0,
      stock_level: 0,
      amazon_buybox: productData?.warnings?.amz_in_buy_box || false,
      // stock_level: productData?.stock_level || 0,
    },
  });

  // Get color for battery based on score percentage
  const getBatteryColor = (percentage: number): string => {
    if (percentage < 0.5) return "bg-red-500";
    if (percentage < 0.7) return "bg-yellow-500";
    return "bg-green-500";
  };

  // Get text color for score value
  const getScoreTextColor = (value: string): string => {
    switch (value.toLowerCase()) {
      case "excellent":
        return "text-green-600";
      case "good":
        return "text-green-500";
      case "average":
        return "text-yellow-500";
      case "weak":
        return "text-red-500";
      case "poor":
        return "text-red-500";
      case "very poor":
        return "text-red-600";
      default:
        return "text-gray-700";
    }
  };

  // console.log("isLoading", isLoading);
  // console.log("scoreData", scoreData);

  // useEffect(() => {
  //   setMounted(true);
  // }, []);

  // if (!mounted) {
  //   return null; // Return null on server-side and first render
  // }

  return (
    <div className=" overflow-hidden bg-white shadow">
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
      />
      <div className="">
        {!user ? (
          <div className="flex flex-col items-center justify-center py-4 bg-gray-50 rounded">
            <Lock size={24} className="text-gray-400 mb-2" />
            <p className="text-sm text-gray-600 mb-1 text-center">
              Login to view AI analysis
            </p>
            <Button
              onClick={() => setIsLoginModalOpen(true)}
              className="bg-black text-white px-3 py-1 rounded text-sm hover:bg-gray-800"
            >
              Login
            </Button>
          </div>
        ) : (
          <>
            <div className="p-2">
              <div className="grid grid-cols-3 gap-2 ">
                <div className="text-center col-span-1 border border-black rounded-md py-1 bg-gray-100 ">
                  <div className="font-bold text-[10px] flex items-center justify-center gap-1">
                    <TrendingUp size={15} /> Est. Sales
                  </div>
                  <div className="text-md font-bold">
                    {formatEstimatedSales(productData?.estimated_sales || 0)}
                  </div>
                </div>
                <div className="text-center col-span-1 border border-black rounded-md py-1 bg-gray-100 ">
                  <div className="font-bold text-[10px]">Gated</div>
                  <div
                    className={`text-md font-bold ${isGated === undefined ? "text-black" : isGated ? "text-red-600" : "text-green-600"}`}
                  >
                    {isGated === undefined ? (
                      <div className="flex items-center justify-center gap-1">
                        N/A
                      </div>
                    ) : isGated ? (
                      <div className="flex items-center gap-1 justify-center">
                        <div className="w-fit h-fit rounded-full border border-red-600 p-[1px]">
                          <X size={14} />
                        </div>
                        Yes
                      </div>
                    ) : (
                      <div className="flex items-center justify-center gap-1">
                        <div className="w-fit h-fit rounded-full border border-green-600 p-[1px]">
                          <Check size={14} />
                        </div>
                        No
                      </div>
                    )}
                  </div>
                </div>
                <div className="text-center col-span-1 border border-black rounded-md py-1 bg-gray-100 ">
                  <div className="font-bold text-[10px]">Amazon In Buy Box</div>
                  <div
                    className={`text-md font-bold ${productData && productData.warnings && productData.warnings.amz_in_buy_box ? "text-red-600" : "text-green-600"}`}
                  >
                    {productData &&
                    productData.warnings &&
                    productData.warnings.amz_in_buy_box
                      ? "Yes"
                      : "No"}
                  </div>
                </div>
              </div>

              <div className="mb-2 border my-2 p-2 flex flex-col gap-1 rounded-md">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xs flex items-center gap-1">
                    <Wand size={14} /> CheckOra Product Score
                  </span>
                  {isLoading ? (
                    <div className="flex items-center text-sm text-gray-600">
                      <Loader size={14} className="mr-1 animate-spin" />
                      Calculating...
                    </div>
                  ) : (
                    <span
                      className={`text-xs  ${scoreData && getScoreTextColor(scoreData.value)}`}
                    >
                      {scoreData && scoreData.value} (
                      {scoreData && scoreData.percentage.toFixed(1)}%)
                    </span>
                  )}
                </div>
                <div className="w-full bg-gray-200 mb-1 border border-gray-400 overflow-hidden rounded-full h-4">
                  {isLoading ? (
                    <div className="h-4 w-full bg-gray-300 animate-pulse rounded-full"></div>
                  ) : (
                    <div
                      className="h-4 rounded-full -ms-[1px] -mt-[1px] transition-all duration-1000 ease-out border border-gray-400"
                      style={{
                        width: `${scoreData && scoreData.percentage}%`,
                        backgroundColor:
                          (scoreData && scoreData.color) ||
                          (scoreData && scoreData.percentage < 0.5
                            ? "#ef4444"
                            : scoreData && scoreData.percentage < 0.7
                              ? "#eab308"
                              : "#22c55e"),
                      }}
                    ></div>
                  )}
                </div>
                <button
                  className="w-full bg-black text-white py-[2px] rounded font-bold flex items-center justify-center gap-1"
                  onClick={() => setIsChatOpen(true)}
                >
                  <Bot /> Initiate CheckOra
                </button>
              </div>

              <div className="grid grid-cols-2 gap-1">
                <button className="text-black font-bold border border-black py-[2px] text-xs rounded text-xs flex items-center justify-center gap-1">
                  <Bot size={14} /> Explore InspectOra
                </button>
                <button className=" text-black font-bold border border-black py-[2px] text-xs rounded text-xs flex items-center justify-center gap-1">
                  <Bot size={14} /> Explore SourceOra
                </button>
              </div>
            </div>

            {isChatOpen && (
              <ChatBot
                isOpen={isChatOpen}
                onClose={() => setIsChatOpen(false)}
              />
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default AISummarizer;
