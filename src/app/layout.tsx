// src/app/layout.tsx
import { AuthProvider } from "@/hooks/useAuth";
import "./globals.css";
import { ReactNode } from "react";
import { ProductProvider } from "@/hooks/useProduct";
import ReactQueryProviders from "@/lib/react-query/provider";

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="en">
      <head>
        <meta name="robots" content="noindex, nofollow" />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
        />
      </head>
      <body className="bg-gray-50 overflow-hidden">
        <AuthProvider>
          <ProductProvider>
            <ReactQueryProviders>{children}</ReactQueryProviders>
          </ProductProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
